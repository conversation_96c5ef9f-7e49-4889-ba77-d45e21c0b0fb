$(document).ready(function() {
    // Navigation handling
    $('.list-group-item[data-section]').click(function(e) {
        e.preventDefault();
        
        const section = $(this).data('section');
        
        // Update active navigation
        $('.list-group-item').removeClass('active');
        $(this).addClass('active');
        
        // Show/hide sections
        $('.content-section').hide();
        $(`#${section}-section`).show().addClass('fade-in');
        
        // Update URL hash
        window.location.hash = section;
    });
    
    // Load section from URL hash
    if (window.location.hash) {
        const section = window.location.hash.substring(1);
        $(`.list-group-item[data-section="${section}"]`).click();
    }
    
    // File upload handling
    $('#uploadForm').submit(function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const $progressCard = $('#uploadProgress');
        const $progressBar = $progressCard.find('.progress-bar');
        const $status = $('#uploadStatus');
        
        // Show progress
        $progressCard.show();
        $progressBar.css('width', '0%');
        $status.html('<i class="fas fa-spinner fa-spin"></i> Preparing upload...');
        
        // AJAX upload
        $.ajax({
            url: 'api/upload.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = (evt.loaded / evt.total) * 100;
                        $progressBar.css('width', percentComplete + '%');
                        $status.html(`<i class="fas fa-upload"></i> Uploading... ${Math.round(percentComplete)}%`);
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                if (response.success) {
                    $progressBar.css('width', '100%').addClass('bg-success');
                    $status.html('<i class="fas fa-check"></i> Upload completed successfully!');
                    
                    // Show uploaded files
                    loadUploadedFiles();
                    
                    // Reset form
                    $('#uploadForm')[0].reset();
                    
                    // Enable next step
                    $('.list-group-item[data-section="extract"]').removeClass('disabled');
                    
                    setTimeout(() => {
                        $progressCard.hide();
                    }, 3000);
                } else {
                    $progressBar.addClass('bg-danger');
                    $status.html(`<i class="fas fa-exclamation-triangle"></i> Upload failed: ${response.message}`);
                }
            },
            error: function(xhr, status, error) {
                $progressBar.addClass('bg-danger');
                $status.html(`<i class="fas fa-exclamation-triangle"></i> Upload error: ${error}`);
            }
        });
    });
    
    // Load uploaded files
    function loadUploadedFiles() {
        $.get('api/get_files.php', function(response) {
            if (response.success && response.files.length > 0) {
                const $filesCard = $('#uploadedFiles');
                const $filesList = $('#filesList');
                
                let filesHtml = '';
                response.files.forEach(file => {
                    const statusClass = `status-${file.extraction_status}`;
                    const statusText = file.extraction_status.charAt(0).toUpperCase() + file.extraction_status.slice(1);
                    const fileIcon = getFileIcon(file.mime_type);
                    
                    filesHtml += `
                        <div class="file-item">
                            <div class="file-icon">
                                <i class="${fileIcon}"></i>
                            </div>
                            <div class="file-info">
                                <div class="file-name">${file.original_name}</div>
                                <div class="file-details">
                                    ${formatFileSize(file.file_size)} • ${file.file_type} • 
                                    Uploaded: ${formatDate(file.created_at)}
                                </div>
                            </div>
                            <div class="file-status ${statusClass}">${statusText}</div>
                        </div>
                    `;
                });
                
                $filesList.html(filesHtml);
                $filesCard.show();
            }
        });
    }
    
    // Data extraction handling
    $('#extractBtn').click(function() {
        const $btn = $(this);
        const $progress = $('#extractionProgress');
        const $progressBar = $progress.find('.progress-bar');
        const $status = $('#extractionStatus');
        
        $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
        $progress.show();
        $progressBar.css('width', '0%');
        
        // Start extraction
        $.post('api/extract.php', function(response) {
            if (response.success) {
                // Simulate progress
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        
                        $progressBar.css('width', '100%').removeClass('progress-bar-animated');
                        $status.html('<i class="fas fa-check"></i> Data extraction completed!');
                        $btn.prop('disabled', false).html('<i class="fas fa-cogs"></i> Start Data Extraction');
                        
                        // Enable next step
                        $('.list-group-item[data-section="review"]').removeClass('disabled');
                        
                        setTimeout(() => {
                            $progress.hide();
                        }, 2000);
                    } else {
                        $progressBar.css('width', progress + '%');
                        $status.html(`<i class="fas fa-cogs"></i> Processing files... ${Math.round(progress)}%`);
                    }
                }, 500);
            } else {
                $btn.prop('disabled', false).html('<i class="fas fa-cogs"></i> Start Data Extraction');
                $progressBar.addClass('bg-danger');
                $status.html(`<i class="fas fa-exclamation-triangle"></i> Extraction failed: ${response.message}`);
            }
        }).fail(function() {
            $btn.prop('disabled', false).html('<i class="fas fa-cogs"></i> Start Data Extraction');
            $progressBar.addClass('bg-danger');
            $status.html('<i class="fas fa-exclamation-triangle"></i> Extraction failed: Server error');
        });
    });
    
    // Load extracted data for review
    function loadExtractedData() {
        $.get('api/get_extracted_data.php', function(response) {
            if (response.success) {
                const $container = $('#extractedData');
                let html = '';
                
                response.data.forEach(item => {
                    const confidenceClass = getConfidenceClass(item.confidence_score);
                    const confidenceText = Math.round(item.confidence_score * 100) + '%';
                    const isRequired = item.is_required ? '<span class="text-danger">*</span>' : '';
                    
                    html += `
                        <div class="data-field">
                            <div class="data-field-label">
                                <span>${item.field_label} ${isRequired}</span>
                                <span class="confidence-badge ${confidenceClass}">
                                    Confidence: ${confidenceText}
                                </span>
                            </div>
                            <div class="mb-2">
                                ${getFieldInput(item)}
                            </div>
                            ${item.source_file ? `<div class="source-file">Source: ${item.source_file}</div>` : ''}
                        </div>
                    `;
                });
                
                $container.html(html);
            }
        });
    }
    
    // Generate field input based on type
    function getFieldInput(item) {
        const value = item.field_value || '';
        const fieldName = item.field_name;
        const dataId = item.id;
        
        switch (item.field_type) {
            case 'textarea':
                return `<textarea class="form-control" data-field="${fieldName}" data-id="${dataId}" rows="3">${value}</textarea>`;
            case 'number':
                return `<input type="number" class="form-control" data-field="${fieldName}" data-id="${dataId}" value="${value}">`;
            case 'date':
                return `<input type="date" class="form-control" data-field="${fieldName}" data-id="${dataId}" value="${value}">`;
            case 'time':
                return `<input type="time" class="form-control" data-field="${fieldName}" data-id="${dataId}" value="${value}">`;
            case 'select':
                const options = JSON.parse(item.field_options || '[]');
                let selectHtml = `<select class="form-control" data-field="${fieldName}" data-id="${dataId}">`;
                selectHtml += '<option value="">Select...</option>';
                options.forEach(option => {
                    const selected = option === value ? 'selected' : '';
                    selectHtml += `<option value="${option}" ${selected}>${option}</option>`;
                });
                selectHtml += '</select>';
                return selectHtml;
            default:
                return `<input type="text" class="form-control" data-field="${fieldName}" data-id="${dataId}" value="${value}">`;
        }
    }
    
    // Handle data updates
    $(document).on('change', '[data-field]', function() {
        const $field = $(this);
        const dataId = $field.data('id');
        const newValue = $field.val();
        
        $.post('api/update_data.php', {
            id: dataId,
            value: newValue
        }, function(response) {
            if (response.success) {
                $field.addClass('border-success');
                setTimeout(() => {
                    $field.removeClass('border-success');
                }, 1000);
            } else {
                $field.addClass('border-danger');
                setTimeout(() => {
                    $field.removeClass('border-danger');
                }, 2000);
            }
        });
    });
    
    // Generate report
    $('#generateBtn').click(function() {
        const $btn = $(this);
        $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating...');
        
        $.post('api/generate_report.php', function(response) {
            if (response.success) {
                $('#reportPreview').html(response.html);
                $btn.prop('disabled', false).html('<i class="fas fa-file-pdf"></i> Generate Report');
                
                // Add download button
                const downloadBtn = `
                    <div class="mt-3">
                        <a href="${response.pdf_url}" class="btn btn-success" target="_blank">
                            <i class="fas fa-download"></i> Download PDF Report
                        </a>
                    </div>
                `;
                $('#reportPreview').append(downloadBtn);
            } else {
                alert('Report generation failed: ' + response.message);
                $btn.prop('disabled', false).html('<i class="fas fa-file-pdf"></i> Generate Report');
            }
        });
    });
    
    // Navigation section handlers
    $('.list-group-item[data-section="review"]').click(function() {
        loadExtractedData();
    });
    
    // Load uploaded files on page load
    loadUploadedFiles();
    
    // Utility functions
    function getFileIcon(mimeType) {
        const icons = {
            'application/pdf': 'fas fa-file-pdf text-danger',
            'application/msword': 'fas fa-file-word text-primary',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fas fa-file-word text-primary',
            'text/plain': 'fas fa-file-alt text-secondary',
            'application/vnd.ms-excel': 'fas fa-file-excel text-success',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fas fa-file-excel text-success',
            'image/jpeg': 'fas fa-file-image text-info',
            'image/png': 'fas fa-file-image text-info',
            'image/gif': 'fas fa-file-image text-info',
            'image/bmp': 'fas fa-file-image text-info'
        };
        return icons[mimeType] || 'fas fa-file text-muted';
    }
    
    function formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
    }
    
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
    
    function getConfidenceClass(score) {
        if (score >= 0.8) return 'confidence-high';
        if (score >= 0.5) return 'confidence-medium';
        return 'confidence-low';
    }
});
