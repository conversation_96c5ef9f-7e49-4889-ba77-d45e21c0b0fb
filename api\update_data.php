<?php
session_start();
require_once '../includes/functions.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    $dataId = $_POST['id'] ?? null;
    $newValue = $_POST['value'] ?? '';
    
    if (!$dataId) {
        throw new Exception('Data ID is required');
    }
    
    $success = updateExtractedData($dataId, $newValue);
    
    if (!$success) {
        throw new Exception('Failed to update data');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Data updated successfully'
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
