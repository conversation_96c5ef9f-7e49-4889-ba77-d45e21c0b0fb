<?php
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/report_generator.php';

class SystemTester {
    private $testResults = [];
    
    public function runAllTests() {
        echo "<h2>Post-Activity Report Generation System - Test Suite</h2>\n";
        
        $this->testDatabaseConnection();
        $this->testFileUpload();
        $this->testDataExtraction();
        $this->testReportGeneration();
        $this->testTemplateFields();
        
        $this->displayResults();
    }
    
    private function testDatabaseConnection() {
        echo "<h3>Testing Database Connection</h3>\n";
        
        try {
            $pdo = getConnection();
            $stmt = $pdo->query("SELECT 1");
            $this->addResult("Database Connection", true, "Successfully connected to database");
            
            // Test table existence
            $tables = ['reports', 'uploaded_files', 'extracted_data', 'template_fields'];
            foreach ($tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $this->addResult("Table: $table", true, "Table exists");
                } else {
                    $this->addResult("Table: $table", false, "Table missing");
                }
            }
            
        } catch (Exception $e) {
            $this->addResult("Database Connection", false, $e->getMessage());
        }
    }
    
    private function testFileUpload() {
        echo "<h3>Testing File Upload Functions</h3>\n";
        
        try {
            // Test directory creation
            createUploadDirectories();
            $this->addResult("Upload Directories", true, "Directories created successfully");
            
            // Test file type detection
            $fileType = getFileTypeCategory('instruction_files');
            if ($fileType === 'instruction') {
                $this->addResult("File Type Detection", true, "Correctly identified instruction file type");
            } else {
                $this->addResult("File Type Detection", false, "Incorrect file type detection");
            }
            
            // Test filename sanitization
            $sanitized = sanitizeFilename('test file with spaces & special chars!.pdf');
            if (preg_match('/^[a-zA-Z0-9._-]+$/', $sanitized)) {
                $this->addResult("Filename Sanitization", true, "Filename properly sanitized");
            } else {
                $this->addResult("Filename Sanitization", false, "Filename sanitization failed");
            }
            
        } catch (Exception $e) {
            $this->addResult("File Upload Functions", false, $e->getMessage());
        }
    }
    
    private function testDataExtraction() {
        echo "<h3>Testing Data Extraction</h3>\n";
        
        try {
            // Test text extraction from sample text
            $sampleText = "Course Title: Digital Transformation Workshop\nDate: March 15, 2024\nVenue: DICT Office";
            $keywords = ['course title', 'date', 'venue'];
            
            foreach ($keywords as $keyword) {
                $extracted = extractDataFromText($sampleText, [$keyword]);
                if (!empty($extracted)) {
                    $this->addResult("Extract: $keyword", true, "Successfully extracted: " . $extracted[0]);
                } else {
                    $this->addResult("Extract: $keyword", false, "Failed to extract data");
                }
            }
            
        } catch (Exception $e) {
            $this->addResult("Data Extraction", false, $e->getMessage());
        }
    }
    
    private function testReportGeneration() {
        echo "<h3>Testing Report Generation</h3>\n";
        
        try {
            // Create test session with sample data
            $sessionId = 'test_session_' . uniqid();
            
            // Create sample report
            $pdo = getConnection();
            $stmt = $pdo->prepare("INSERT INTO reports (session_id, status) VALUES (?, 'draft')");
            $stmt->execute([$sessionId]);
            $reportId = $pdo->lastInsertId();
            
            // Add sample data
            $sampleData = [
                'course_title' => 'Test Course',
                'course_code' => 'TEST-001',
                'date' => '2024-03-15',
                'venue' => 'Test Venue'
            ];
            
            $stmt = $pdo->prepare("INSERT INTO extracted_data (report_id, field_name, field_value, confidence_score) VALUES (?, ?, ?, ?)");
            foreach ($sampleData as $field => $value) {
                $stmt->execute([$reportId, $field, $value, 0.9]);
            }
            
            // Test report generation
            $generator = new ReportGenerator($sessionId);
            $html = $generator->generateHTML();
            
            if (strpos($html, 'Test Course') !== false) {
                $this->addResult("Report Generation", true, "HTML report generated successfully");
            } else {
                $this->addResult("Report Generation", false, "Report generation failed - data not populated");
            }
            
            // Test PDF generation
            $pdfPath = $generator->generatePDF();
            if (file_exists($pdfPath)) {
                $this->addResult("PDF Generation", true, "PDF file created at: $pdfPath");
            } else {
                $this->addResult("PDF Generation", false, "PDF file not created");
            }
            
            // Cleanup
            $stmt = $pdo->prepare("DELETE FROM reports WHERE id = ?");
            $stmt->execute([$reportId]);
            
        } catch (Exception $e) {
            $this->addResult("Report Generation", false, $e->getMessage());
        }
    }
    
    private function testTemplateFields() {
        echo "<h3>Testing Template Fields</h3>\n";
        
        try {
            $fields = getTemplateFields();
            
            if (count($fields) > 0) {
                $this->addResult("Template Fields", true, "Found " . count($fields) . " template fields");
                
                // Check required fields
                $requiredFields = ['course_title', 'course_code', 'date', 'venue'];
                foreach ($requiredFields as $field) {
                    $found = false;
                    foreach ($fields as $templateField) {
                        if ($templateField['field_name'] === $field) {
                            $found = true;
                            break;
                        }
                    }
                    
                    if ($found) {
                        $this->addResult("Required Field: $field", true, "Field exists in template");
                    } else {
                        $this->addResult("Required Field: $field", false, "Field missing from template");
                    }
                }
            } else {
                $this->addResult("Template Fields", false, "No template fields found");
            }
            
        } catch (Exception $e) {
            $this->addResult("Template Fields", false, $e->getMessage());
        }
    }
    
    private function addResult($test, $passed, $message) {
        $this->testResults[] = [
            'test' => $test,
            'passed' => $passed,
            'message' => $message
        ];
        
        $status = $passed ? "✓ PASS" : "✗ FAIL";
        $color = $passed ? "green" : "red";
        echo "<p style='color: $color;'><strong>$status</strong> - $test: $message</p>\n";
    }
    
    private function displayResults() {
        echo "<h3>Test Summary</h3>\n";
        
        $totalTests = count($this->testResults);
        $passedTests = array_filter($this->testResults, function($result) {
            return $result['passed'];
        });
        $passedCount = count($passedTests);
        $failedCount = $totalTests - $passedCount;
        
        echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>Results:</h4>\n";
        echo "<p><strong>Total Tests:</strong> $totalTests</p>\n";
        echo "<p style='color: green;'><strong>Passed:</strong> $passedCount</p>\n";
        echo "<p style='color: red;'><strong>Failed:</strong> $failedCount</p>\n";
        echo "<p><strong>Success Rate:</strong> " . round(($passedCount / $totalTests) * 100, 2) . "%</p>\n";
        echo "</div>\n";
        
        if ($failedCount > 0) {
            echo "<h4>Failed Tests:</h4>\n";
            foreach ($this->testResults as $result) {
                if (!$result['passed']) {
                    echo "<p style='color: red;'>• {$result['test']}: {$result['message']}</p>\n";
                }
            }
        }
    }
}

// Run tests if accessed directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "<!DOCTYPE html><html><head><title>System Test</title></head><body>";
    $tester = new SystemTester();
    $tester->runAllTests();
    echo "</body></html>";
}
?>
