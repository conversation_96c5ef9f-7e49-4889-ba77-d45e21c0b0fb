<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'post_activity_reports');
define('DB_USER', 'root');
define('DB_PASS', '');

// Create database connection
function getConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", 
                       DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
        return $pdo;
    } catch (PDOException $e) {
        // Try to create database if it doesn't exist
        try {
            $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=utf8mb4", DB_USER, DB_PASS);
            $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE " . DB_NAME);
            return $pdo;
        } catch (PDOException $e2) {
            die("Database connection failed: " . $e2->getMessage());
        }
    }
}

// Initialize database tables
function initializeDatabase() {
    $pdo = getConnection();
    
    // Reports table
    $pdo->exec("CREATE TABLE IF NOT EXISTS reports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(255) NOT NULL,
        report_data JSON,
        template_data JSON,
        status ENUM('draft', 'completed') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_session (session_id),
        INDEX idx_status (status)
    )");
    
    // Uploaded files table
    $pdo->exec("CREATE TABLE IF NOT EXISTS uploaded_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(255) NOT NULL,
        report_id INT,
        file_type ENUM('instruction', 'design', 'evaluation', 'image', 'additional') NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        stored_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        mime_type VARCHAR(100),
        extracted_text LONGTEXT,
        extraction_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
        INDEX idx_session (session_id),
        INDEX idx_type (file_type),
        INDEX idx_status (extraction_status)
    )");
    
    // Extracted data table
    $pdo->exec("CREATE TABLE IF NOT EXISTS extracted_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        report_id INT NOT NULL,
        field_name VARCHAR(100) NOT NULL,
        field_value TEXT,
        confidence_score DECIMAL(3,2) DEFAULT 0.00,
        source_file_id INT,
        is_verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (report_id) REFERENCES reports(id) ON DELETE CASCADE,
        FOREIGN KEY (source_file_id) REFERENCES uploaded_files(id) ON DELETE SET NULL,
        INDEX idx_report (report_id),
        INDEX idx_field (field_name),
        INDEX idx_verified (is_verified)
    )");
    
    // Template fields configuration
    $pdo->exec("CREATE TABLE IF NOT EXISTS template_fields (
        id INT AUTO_INCREMENT PRIMARY KEY,
        field_name VARCHAR(100) NOT NULL UNIQUE,
        field_label VARCHAR(200) NOT NULL,
        field_type ENUM('text', 'number', 'date', 'time', 'textarea', 'select') DEFAULT 'text',
        field_options JSON,
        extraction_keywords JSON,
        is_required BOOLEAN DEFAULT FALSE,
        display_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_order (display_order)
    )");
    
    // Insert default template fields
    insertDefaultTemplateFields($pdo);
}

// Insert default template fields based on the After Training Report template
function insertDefaultTemplateFields($pdo) {
    $fields = [
        // Training Details Section
        ['course_title', 'Course Title', 'text', null, '["course", "title", "training", "program"]', true, 1],
        ['course_code', 'Course Code', 'text', null, '["code", "course code", "program code"]', true, 2],
        ['date', 'Date', 'date', null, '["date", "training date", "event date"]', true, 3],
        ['time', 'Time', 'time', null, '["time", "schedule", "duration"]', true, 4],
        ['duration', 'Duration', 'text', null, '["duration", "hours", "days"]', true, 5],
        ['venue', 'Venue', 'text', null, '["venue", "location", "place"]', true, 6],
        ['resource_person', 'Resource Person', 'text', null, '["resource person", "trainer", "facilitator", "speaker"]', true, 7],
        ['platform_used', 'Platform Used', 'text', null, '["platform", "zoom", "teams", "meet"]', false, 8],
        ['mode', 'Mode', 'select', '["Online", "Face-to-face", "Hybrid"]', '["mode", "delivery", "format"]', true, 9],
        ['target_participants', 'Target Participants', 'text', null, '["participants", "target", "audience"]', true, 10],
        
        // Attendance Information
        ['total_attendees', 'Total # of Attendees', 'number', null, '["total", "attendees", "participants"]', true, 11],
        ['male_attendees', 'Male Attendees', 'number', null, '["male", "men"]', false, 12],
        ['female_attendees', 'Female Attendees', 'number', null, '["female", "women"]', false, 13],
        ['nga_male', 'NGA Male', 'number', null, '["nga", "national government"]', false, 14],
        ['nga_female', 'NGA Female', 'number', null, '["nga", "national government"]', false, 15],
        ['lgu_male', 'LGU Male', 'number', null, '["lgu", "local government"]', false, 16],
        ['lgu_female', 'LGU Female', 'number', null, '["lgu", "local government"]', false, 17],
        ['suc_male', 'SUC Male', 'number', null, '["suc", "state university"]', false, 18],
        ['suc_female', 'SUC Female', 'number', null, '["suc", "state university"]', false, 19],
        ['others_male', 'Others Male', 'number', null, '["others", "private"]', false, 20],
        ['others_female', 'Others Female', 'number', null, '["others", "private"]', false, 21],
        ['certificates_male', 'Certificates Issued Male', 'number', null, '["certificates", "issued"]', false, 22],
        ['certificates_female', 'Certificates Issued Female', 'number', null, '["certificates", "issued"]', false, 23],
        
        // Content Sections
        ['rationale', 'Rationale', 'textarea', null, '["rationale", "background", "purpose"]', true, 24],
        ['objectives', 'Objectives', 'textarea', null, '["objectives", "goals", "aims"]', true, 25],
        ['topics_covered', 'Topics Covered', 'textarea', null, '["topics", "covered", "agenda", "modules"]', true, 26],
        ['issues_concerns', 'Issues and Concerns', 'textarea', null, '["issues", "concerns", "problems"]', false, 27],
        ['recommendations', 'Recommendations', 'textarea', null, '["recommendations", "suggestions"]', false, 28],
        ['plans_action_items', 'Plans and Action Items', 'textarea', null, '["plans", "action items", "next steps"]', false, 29],
        
        // Signature Section
        ['prepared_by', 'Prepared by', 'text', null, '["prepared by", "author"]', true, 30],
        ['prepared_by_position', 'Prepared by Position', 'text', null, '["position", "title", "designation"]', false, 31],
        ['noted_by', 'Noted by', 'text', null, '["noted by", "approved by"]', true, 32],
        ['noted_by_position', 'Noted by Position', 'text', null, '["position", "title", "designation"]', false, 33]
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO template_fields 
        (field_name, field_label, field_type, field_options, extraction_keywords, is_required, display_order) 
        VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($fields as $field) {
        $stmt->execute($field);
    }
}
?>
