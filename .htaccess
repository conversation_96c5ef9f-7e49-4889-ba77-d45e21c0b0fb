# Post-Activity Report Generation System
# Apache Configuration

# Enable rewrite engine
RewriteEngine On

# Security: Prevent access to sensitive files
<Files "*.php~">
    Order allow,deny
    Den<PERSON> from all
</Files>

<Files "*.inc">
    Order allow,deny
    Deny from all
</Files>

# Protect config files
<FilesMatch "^(config|includes)/">
    Order allow,deny
    Deny from all
</FilesMatch>

# Allow access to API endpoints
<FilesMatch "^api/">
    Order allow,deny
    Allow from all
</FilesMatch>

# Set upload limits (if not set in php.ini)
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value max_input_time 300

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Error pages
ErrorDocument 404 /post/404.html
ErrorDocument 500 /post/500.html
