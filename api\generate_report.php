<?php
session_start();
require_once '../includes/report_generator.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    $generator = new ReportGenerator();
    $html = $generator->generateHTML();
    $pdfPath = $generator->generatePDF();
    
    echo json_encode([
        'success' => true,
        'message' => 'Report generated successfully',
        'html' => $html,
        'pdf_url' => $pdfPath
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
