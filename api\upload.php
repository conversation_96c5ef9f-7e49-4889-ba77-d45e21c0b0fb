<?php
session_start();
require_once '../includes/functions.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    if (empty($_FILES)) {
        throw new Exception('No files uploaded');
    }
    
    $uploadedFiles = [];
    $errors = [];
    
    // Process each file type
    $fileTypes = ['instruction_files', 'design_files', 'evaluation_files', 'image_files', 'additional_files'];
    
    foreach ($fileTypes as $fileType) {
        if (isset($_FILES[$fileType]) && is_array($_FILES[$fileType]['name'])) {
            $fileCategory = getFileTypeCategory($fileType);
            
            for ($i = 0; $i < count($_FILES[$fileType]['name']); $i++) {
                if ($_FILES[$fileType]['error'][$i] === UPLOAD_ERR_OK) {
                    $file = [
                        'name' => $_FILES[$fileType]['name'][$i],
                        'type' => $_FILES[$fileType]['type'][$i],
                        'tmp_name' => $_FILES[$fileType]['tmp_name'][$i],
                        'error' => $_FILES[$fileType]['error'][$i],
                        'size' => $_FILES[$fileType]['size'][$i]
                    ];
                    
                    try {
                        $result = handleFileUpload($file, $fileCategory);
                        $uploadedFiles[] = $result;
                    } catch (Exception $e) {
                        $errors[] = "Failed to upload {$file['name']}: " . $e->getMessage();
                    }
                }
            }
        }
    }
    
    if (empty($uploadedFiles) && !empty($errors)) {
        throw new Exception('All file uploads failed: ' . implode(', ', $errors));
    }
    
    $response = [
        'success' => true,
        'message' => count($uploadedFiles) . ' files uploaded successfully',
        'files' => $uploadedFiles
    ];
    
    if (!empty($errors)) {
        $response['warnings'] = $errors;
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
