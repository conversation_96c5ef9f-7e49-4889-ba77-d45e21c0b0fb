<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Sample data for testing the system
function insertSampleData() {
    $sessionId = 'sample_session_' . uniqid();
    
    // Create a sample report
    $pdo = getConnection();
    $stmt = $pdo->prepare("INSERT INTO reports (session_id, status) VALUES (?, 'draft')");
    $stmt->execute([$sessionId]);
    $reportId = $pdo->lastInsertId();
    
    // Sample extracted data
    $sampleData = [
        'course_title' => 'Digital Transformation and Innovation Workshop',
        'course_code' => 'DTI-2024-001',
        'date' => '2024-03-15',
        'time' => '09:00 AM - 05:00 PM',
        'duration' => '8 hours',
        'venue' => 'DICT Regional Office, Quezon City',
        'resource_person' => 'Dr. <PERSON>, IT Specialist',
        'platform_used' => 'Zoom',
        'mode' => 'Hybrid',
        'target_participants' => 'DICT Personnel and Partner Agencies',
        'total_attendees' => '45',
        'male_attendees' => '20',
        'female_attendees' => '25',
        'nga_male' => '8',
        'nga_female' => '12',
        'lgu_male' => '7',
        'lgu_female' => '8',
        'suc_male' => '3',
        'suc_female' => '3',
        'others_male' => '2',
        'others_female' => '2',
        'certificates_male' => '18',
        'certificates_female' => '23',
        'rationale' => 'The Digital Transformation and Innovation Workshop was conducted to enhance the digital literacy and technological capabilities of government personnel. This training aims to equip participants with the necessary skills and knowledge to effectively implement digital solutions in their respective offices and improve public service delivery.',
        'objectives' => '1. To introduce participants to the latest digital transformation trends and technologies
2. To provide hands-on experience with digital tools and platforms
3. To develop strategic thinking for digital innovation in government services
4. To foster collaboration and knowledge sharing among participants
5. To create action plans for implementing digital solutions in their organizations',
        'topics_covered' => '• Introduction to Digital Transformation
• Cloud Computing Fundamentals
• Data Analytics and Visualization
• Cybersecurity Best Practices
• Digital Project Management
• Innovation Methodologies
• Case Studies of Successful Digital Implementations
• Hands-on Workshop: Building Digital Solutions
• Action Planning and Next Steps',
        'issues_concerns' => '• Limited internet connectivity in some remote areas
• Need for additional technical support during implementation
• Budget constraints for acquiring new technologies
• Resistance to change from some staff members
• Need for follow-up training sessions',
        'recommendations' => '• Conduct follow-up training sessions within 3 months
• Establish a technical support helpdesk
• Create a community of practice for continuous learning
• Develop a phased implementation plan for digital solutions
• Allocate budget for necessary technology upgrades',
        'plans_action_items' => '• Schedule follow-up sessions by June 2024
• Create digital transformation roadmaps for each participating office
• Establish monthly progress monitoring meetings
• Develop training materials for cascade training
• Set up online collaboration platforms for participants',
        'prepared_by' => 'JAYKEE M.ABA-A',
        'prepared_by_position' => 'Project Development Officer I',
        'noted_by' => 'ENGR. ELIZALDE S. RAMOS',
        'noted_by_position' => 'ITO I - Provincial Officer, SDS'
    ];
    
    // Insert sample data
    $stmt = $pdo->prepare("INSERT INTO extracted_data 
        (report_id, field_name, field_value, confidence_score, is_verified) 
        VALUES (?, ?, ?, ?, ?)");
    
    foreach ($sampleData as $fieldName => $value) {
        $stmt->execute([$reportId, $fieldName, $value, 0.95, true]);
    }
    
    echo "Sample data inserted successfully!\n";
    echo "Session ID: $sessionId\n";
    echo "Report ID: $reportId\n";
    
    return $sessionId;
}

// Run if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    insertSampleData();
}
?>
