<?php
require_once 'config/database.php';

// Generate unique session ID for file uploads
function getSessionId() {
    if (!isset($_SESSION['upload_session'])) {
        $_SESSION['upload_session'] = uniqid('session_', true);
    }
    return $_SESSION['upload_session'];
}

// Create upload directories if they don't exist
function createUploadDirectories() {
    $directories = [
        'uploads',
        'uploads/instruction',
        'uploads/design', 
        'uploads/evaluation',
        'uploads/images',
        'uploads/additional',
        'generated_reports',
        'temp'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

// Sanitize filename
function sanitizeFilename($filename) {
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
    return substr($filename, 0, 255);
}

// Get file type category based on upload field name
function getFileTypeCategory($fieldName) {
    if (strpos($fieldName, 'instruction') !== false) return 'instruction';
    if (strpos($fieldName, 'design') !== false) return 'design';
    if (strpos($fieldName, 'evaluation') !== false) return 'evaluation';
    if (strpos($fieldName, 'image') !== false) return 'image';
    return 'additional';
}

// Handle file upload
function handleFileUpload($file, $fileType) {
    createUploadDirectories();
    
    $sessionId = getSessionId();
    $originalName = $file['name'];
    $tempPath = $file['tmp_name'];
    $fileSize = $file['size'];
    $mimeType = $file['type'];
    
    // Validate file
    $allowedTypes = [
        'instruction' => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
        'design' => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
        'evaluation' => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
        'image' => ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'],
        'additional' => [] // Allow all types for additional files
    ];
    
    if ($fileType !== 'additional' && !in_array($mimeType, $allowedTypes[$fileType])) {
        throw new Exception("Invalid file type for $fileType: $mimeType");
    }
    
    // Generate unique filename
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $storedName = uniqid() . '_' . sanitizeFilename(pathinfo($originalName, PATHINFO_FILENAME)) . '.' . $extension;
    $uploadPath = "uploads/$fileType/$storedName";
    
    // Move uploaded file
    if (!move_uploaded_file($tempPath, $uploadPath)) {
        throw new Exception("Failed to move uploaded file");
    }
    
    // Save file information to database
    $pdo = getConnection();
    $stmt = $pdo->prepare("INSERT INTO uploaded_files 
        (session_id, file_type, original_name, stored_name, file_path, file_size, mime_type) 
        VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->execute([$sessionId, $fileType, $originalName, $storedName, $uploadPath, $fileSize, $mimeType]);
    
    return [
        'id' => $pdo->lastInsertId(),
        'original_name' => $originalName,
        'stored_name' => $storedName,
        'file_path' => $uploadPath,
        'file_size' => $fileSize,
        'mime_type' => $mimeType
    ];
}

// Get uploaded files for current session
function getUploadedFiles($sessionId = null) {
    if (!$sessionId) {
        $sessionId = getSessionId();
    }
    
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT * FROM uploaded_files WHERE session_id = ? ORDER BY created_at DESC");
    $stmt->execute([$sessionId]);
    
    return $stmt->fetchAll();
}

// Extract text from different file types
function extractTextFromFile($filePath, $mimeType) {
    $text = '';
    
    try {
        switch ($mimeType) {
            case 'text/plain':
                $text = file_get_contents($filePath);
                break;
                
            case 'application/pdf':
                // Use pdftotext if available, otherwise try basic extraction
                if (function_exists('shell_exec') && shell_exec('which pdftotext')) {
                    $text = shell_exec("pdftotext '$filePath' -");
                } else {
                    // Fallback: basic PDF text extraction (limited)
                    $content = file_get_contents($filePath);
                    if (preg_match_all('/\((.*?)\)/', $content, $matches)) {
                        $text = implode(' ', $matches[1]);
                    }
                }
                break;
                
            case 'application/msword':
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                // For DOCX files, try to extract from XML
                if ($mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                    $zip = new ZipArchive();
                    if ($zip->open($filePath) === TRUE) {
                        $xml = $zip->getFromName('word/document.xml');
                        if ($xml) {
                            $dom = new DOMDocument();
                            $dom->loadXML($xml);
                            $text = $dom->textContent;
                        }
                        $zip->close();
                    }
                }
                break;
                
            case 'application/vnd.ms-excel':
            case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                // Basic Excel extraction - would need PHPSpreadsheet for full support
                $text = "Excel file content extraction requires additional libraries";
                break;
        }
    } catch (Exception $e) {
        error_log("Text extraction failed for $filePath: " . $e->getMessage());
        $text = "Text extraction failed: " . $e->getMessage();
    }
    
    return trim($text);
}

// Update file with extracted text
function updateFileWithExtractedText($fileId, $extractedText) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("UPDATE uploaded_files SET extracted_text = ?, extraction_status = 'completed' WHERE id = ?");
    $stmt->execute([$extractedText, $fileId]);
}

// Get template fields
function getTemplateFields() {
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM template_fields ORDER BY display_order");
    return $stmt->fetchAll();
}

// Extract data from text using keywords
function extractDataFromText($text, $keywords) {
    $extractedData = [];
    $text = strtolower($text);
    
    foreach ($keywords as $keyword) {
        $keyword = strtolower($keyword);
        $pattern = '/(?:' . preg_quote($keyword, '/') . ')[\s:]*([^\n\r.;,]{1,100})/i';
        
        if (preg_match($pattern, $text, $matches)) {
            $value = trim($matches[1]);
            if (!empty($value)) {
                $extractedData[] = $value;
            }
        }
    }
    
    return $extractedData;
}

// Process all uploaded files for data extraction
function processFilesForExtraction($sessionId = null) {
    if (!$sessionId) {
        $sessionId = getSessionId();
    }
    
    $files = getUploadedFiles($sessionId);
    $templateFields = getTemplateFields();
    
    foreach ($files as $file) {
        if ($file['extraction_status'] === 'pending') {
            // Update status to processing
            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE uploaded_files SET extraction_status = 'processing' WHERE id = ?");
            $stmt->execute([$file['id']]);
            
            try {
                // Extract text from file
                $extractedText = extractTextFromFile($file['file_path'], $file['mime_type']);
                updateFileWithExtractedText($file['id'], $extractedText);
                
                // Extract data based on template fields
                foreach ($templateFields as $field) {
                    $keywords = json_decode($field['extraction_keywords'], true) ?: [];
                    $extractedValues = extractDataFromText($extractedText, $keywords);
                    
                    if (!empty($extractedValues)) {
                        // Save the best match (first one for now)
                        saveExtractedData($sessionId, $field['field_name'], $extractedValues[0], 0.8, $file['id']);
                    }
                }
                
            } catch (Exception $e) {
                // Update status to failed
                $stmt = $pdo->prepare("UPDATE uploaded_files SET extraction_status = 'failed' WHERE id = ?");
                $stmt->execute([$file['id']]);
                error_log("File processing failed for file ID {$file['id']}: " . $e->getMessage());
            }
        }
    }
}

// Save extracted data
function saveExtractedData($sessionId, $fieldName, $fieldValue, $confidence, $sourceFileId) {
    // First, get or create report for this session
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT id FROM reports WHERE session_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$sessionId]);
    $report = $stmt->fetch();
    
    if (!$report) {
        // Create new report
        $stmt = $pdo->prepare("INSERT INTO reports (session_id, status) VALUES (?, 'draft')");
        $stmt->execute([$sessionId]);
        $reportId = $pdo->lastInsertId();
    } else {
        $reportId = $report['id'];
    }
    
    // Check if data already exists for this field
    $stmt = $pdo->prepare("SELECT id FROM extracted_data WHERE report_id = ? AND field_name = ?");
    $stmt->execute([$reportId, $fieldName]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        // Update existing data if confidence is higher
        $stmt = $pdo->prepare("UPDATE extracted_data SET field_value = ?, confidence_score = ?, source_file_id = ? 
                              WHERE id = ? AND confidence_score < ?");
        $stmt->execute([$fieldValue, $confidence, $sourceFileId, $existing['id'], $confidence]);
    } else {
        // Insert new data
        $stmt = $pdo->prepare("INSERT INTO extracted_data 
            (report_id, field_name, field_value, confidence_score, source_file_id) 
            VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$reportId, $fieldName, $fieldValue, $confidence, $sourceFileId]);
    }
}

// Get extracted data for review
function getExtractedDataForReview($sessionId = null) {
    if (!$sessionId) {
        $sessionId = getSessionId();
    }
    
    $pdo = getConnection();
    $stmt = $pdo->prepare("
        SELECT ed.*, tf.field_label, tf.field_type, tf.field_options, tf.is_required,
               uf.original_name as source_file
        FROM extracted_data ed
        JOIN reports r ON ed.report_id = r.id
        JOIN template_fields tf ON ed.field_name = tf.field_name
        LEFT JOIN uploaded_files uf ON ed.source_file_id = uf.id
        WHERE r.session_id = ?
        ORDER BY tf.display_order
    ");
    $stmt->execute([$sessionId]);
    
    return $stmt->fetchAll();
}

// Update extracted data
function updateExtractedData($dataId, $newValue) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("UPDATE extracted_data SET field_value = ?, is_verified = TRUE WHERE id = ?");
    return $stmt->execute([$newValue, $dataId]);
}

// Format file size
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

// Get file icon based on mime type
function getFileIcon($mimeType) {
    $icons = [
        'application/pdf' => 'fas fa-file-pdf text-danger',
        'application/msword' => 'fas fa-file-word text-primary',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'fas fa-file-word text-primary',
        'text/plain' => 'fas fa-file-alt text-secondary',
        'application/vnd.ms-excel' => 'fas fa-file-excel text-success',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'fas fa-file-excel text-success',
        'image/jpeg' => 'fas fa-file-image text-info',
        'image/png' => 'fas fa-file-image text-info',
        'image/gif' => 'fas fa-file-image text-info',
        'image/bmp' => 'fas fa-file-image text-info'
    ];
    
    return $icons[$mimeType] ?? 'fas fa-file text-muted';
}
?>
