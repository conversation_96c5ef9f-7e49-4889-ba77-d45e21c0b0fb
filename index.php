<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Initialize database if not exists
initializeDatabase();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Post-Activity Report Generation System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row bg-primary text-white py-3 mb-4">
            <div class="col-12">
                <div class="d-flex align-items-center">
                    <img src="assets/images/dict-logo.png" alt="DICT Logo" class="me-3" style="height: 60px;">
                    <div>
                        <h2 class="mb-0">Post-Activity Report Generation System</h2>
                        <p class="mb-0">Department of Information and Communications Technology</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <div class="col-md-3">
                <!-- Sidebar Navigation -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bars"></i> Navigation</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <a href="#upload" class="list-group-item list-group-item-action active" data-section="upload">
                                <i class="fas fa-upload"></i> Upload Files
                            </a>
                            <a href="#extract" class="list-group-item list-group-item-action" data-section="extract">
                                <i class="fas fa-search"></i> Extract Data
                            </a>
                            <a href="#review" class="list-group-item list-group-item-action" data-section="review">
                                <i class="fas fa-edit"></i> Review & Edit
                            </a>
                            <a href="#generate" class="list-group-item list-group-item-action" data-section="generate">
                                <i class="fas fa-file-pdf"></i> Generate Report
                            </a>
                            <a href="#history" class="list-group-item list-group-item-action" data-section="history">
                                <i class="fas fa-history"></i> Report History
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <!-- Upload Section -->
                <div id="upload-section" class="content-section">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-upload"></i> Upload Activity Files</h4>
                            <p class="mb-0 text-muted">Upload your activity-related documents and images</p>
                        </div>
                        <div class="card-body">
                            <form id="uploadForm" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Point of Instruction Documents</label>
                                            <input type="file" class="form-control" name="instruction_files[]" 
                                                   accept=".pdf,.doc,.docx,.txt" multiple>
                                            <div class="form-text">PDF, DOC, DOCX, TXT files</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Activity Design Documents</label>
                                            <input type="file" class="form-control" name="design_files[]" 
                                                   accept=".pdf,.doc,.docx,.txt" multiple>
                                            <div class="form-text">PDF, DOC, DOCX, TXT files</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Evaluation Forms/Results</label>
                                            <input type="file" class="form-control" name="evaluation_files[]" 
                                                   accept=".pdf,.doc,.docx,.txt,.xlsx,.xls" multiple>
                                            <div class="form-text">PDF, DOC, DOCX, TXT, Excel files</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Supporting Images</label>
                                            <input type="file" class="form-control" name="image_files[]" 
                                                   accept=".jpg,.jpeg,.png,.gif,.bmp" multiple>
                                            <div class="form-text">JPG, PNG, GIF, BMP files</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Additional Materials</label>
                                    <input type="file" class="form-control" name="additional_files[]" multiple>
                                    <div class="form-text">Any additional supporting documents</div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload"></i> Upload Files
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Upload Progress -->
                    <div id="uploadProgress" class="card mt-3" style="display: none;">
                        <div class="card-body">
                            <h6>Upload Progress</h6>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="uploadStatus" class="mt-2"></div>
                        </div>
                    </div>

                    <!-- Uploaded Files List -->
                    <div id="uploadedFiles" class="card mt-3" style="display: none;">
                        <div class="card-header">
                            <h5>Uploaded Files</h5>
                        </div>
                        <div class="card-body">
                            <div id="filesList"></div>
                        </div>
                    </div>
                </div>

                <!-- Extract Section -->
                <div id="extract-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-search"></i> Data Extraction</h4>
                            <p class="mb-0 text-muted">Automatically extract information from uploaded files</p>
                        </div>
                        <div class="card-body">
                            <button id="extractBtn" class="btn btn-success">
                                <i class="fas fa-cogs"></i> Start Data Extraction
                            </button>
                            <div id="extractionProgress" class="mt-3" style="display: none;">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                                <div id="extractionStatus" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Review Section -->
                <div id="review-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-edit"></i> Review & Edit Extracted Data</h4>
                            <p class="mb-0 text-muted">Review and modify the extracted information before generating the report</p>
                        </div>
                        <div class="card-body">
                            <div id="extractedData"></div>
                        </div>
                    </div>
                </div>

                <!-- Generate Section -->
                <div id="generate-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-file-pdf"></i> Generate Report</h4>
                            <p class="mb-0 text-muted">Generate the final post-activity report</p>
                        </div>
                        <div class="card-body">
                            <button id="generateBtn" class="btn btn-primary">
                                <i class="fas fa-file-pdf"></i> Generate Report
                            </button>
                            <div id="reportPreview" class="mt-3"></div>
                        </div>
                    </div>
                </div>

                <!-- History Section -->
                <div id="history-section" class="content-section" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-history"></i> Report History</h4>
                            <p class="mb-0 text-muted">View previously generated reports</p>
                        </div>
                        <div class="card-body">
                            <div id="reportHistory"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
