<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Setup - Post-Activity Report Generation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-cog"></i> System Setup</h3>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            echo "<div class='alert alert-info'>Setting up the Post-Activity Report Generation System...</div>";
                            
                            // Initialize database
                            echo "<p><strong>Step 1:</strong> Initializing database...</p>";
                            initializeDatabase();
                            echo "<div class='alert alert-success'>✓ Database initialized successfully</div>";
                            
                            // Create directories
                            echo "<p><strong>Step 2:</strong> Creating upload directories...</p>";
                            createUploadDirectories();
                            echo "<div class='alert alert-success'>✓ Upload directories created</div>";
                            
                            // Check file permissions
                            echo "<p><strong>Step 3:</strong> Checking file permissions...</p>";
                            $directories = ['uploads', 'generated_reports', 'temp'];
                            $permissionIssues = [];
                            
                            foreach ($directories as $dir) {
                                if (!is_writable($dir)) {
                                    $permissionIssues[] = $dir;
                                }
                            }
                            
                            if (empty($permissionIssues)) {
                                echo "<div class='alert alert-success'>✓ All directories are writable</div>";
                            } else {
                                echo "<div class='alert alert-warning'>⚠ The following directories need write permissions: " . implode(', ', $permissionIssues) . "</div>";
                            }
                            
                            // Test database connection
                            echo "<p><strong>Step 4:</strong> Testing database connection...</p>";
                            $pdo = getConnection();
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM template_fields");
                            $result = $stmt->fetch();
                            echo "<div class='alert alert-success'>✓ Database connection successful. Template fields: " . $result['count'] . "</div>";
                            
                            echo "<div class='alert alert-success mt-4'>";
                            echo "<h5>Setup Complete!</h5>";
                            echo "<p>The Post-Activity Report Generation System has been set up successfully.</p>";
                            echo "<a href='index.php' class='btn btn-primary'>Go to Application</a>";
                            echo "</div>";
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<h5>Setup Failed</h5>";
                            echo "<p>Error: " . $e->getMessage() . "</p>";
                            echo "</div>";
                        }
                        ?>
                        
                        <div class="mt-4">
                            <h5>System Information</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>PHP Version:</strong></td>
                                    <td><?php echo PHP_VERSION; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>MySQL Extension:</strong></td>
                                    <td><?php echo extension_loaded('pdo_mysql') ? '✓ Available' : '✗ Not Available'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>ZIP Extension:</strong></td>
                                    <td><?php echo extension_loaded('zip') ? '✓ Available' : '✗ Not Available'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>GD Extension:</strong></td>
                                    <td><?php echo extension_loaded('gd') ? '✓ Available' : '✗ Not Available'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Max Upload Size:</strong></td>
                                    <td><?php echo ini_get('upload_max_filesize'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Max Post Size:</strong></td>
                                    <td><?php echo ini_get('post_max_size'); ?></td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="mt-4">
                            <h5>Optional: Load Sample Data</h5>
                            <p>You can load sample data to test the system functionality.</p>
                            <a href="utils/sample_data.php" class="btn btn-secondary">Load Sample Data</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
