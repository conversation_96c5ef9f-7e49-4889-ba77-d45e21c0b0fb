<?php
require_once 'functions.php';

class ReportGenerator {
    private $data;
    private $sessionId;
    
    public function __construct($sessionId = null) {
        $this->sessionId = $sessionId ?: getSessionId();
        $this->loadData();
    }
    
    private function loadData() {
        $extractedData = getExtractedDataForReview($this->sessionId);
        $this->data = [];
        
        foreach ($extractedData as $item) {
            $this->data[$item['field_name']] = $item['field_value'] ?: '';
        }
    }
    
    public function generateHTML() {
        $html = $this->getReportTemplate();

        // Replace placeholders with actual data
        foreach ($this->data as $field => $value) {
            $placeholder = '{{' . strtoupper($field) . '}}';
            $html = str_replace($placeholder, htmlspecialchars($value), $html);
        }

        // Handle special calculations
        $html = $this->processCalculatedFields($html);

        // Insert images
        $html = $this->insertImages($html);

        return $html;
    }

    private function insertImages($html) {
        // Get uploaded images for this session
        $pdo = getConnection();
        $stmt = $pdo->prepare("SELECT * FROM uploaded_files WHERE session_id = ? AND file_type = 'image' ORDER BY created_at");
        $stmt->execute([$this->sessionId]);
        $images = $stmt->fetchAll();

        $imageHtml = '';
        if (!empty($images)) {
            $imageHtml = '<div style="display: flex; flex-wrap: wrap; gap: 10px;">';
            foreach ($images as $image) {
                $imageHtml .= '<div style="flex: 1; min-width: 200px; max-width: 300px; text-align: center;">';
                $imageHtml .= '<img src="../' . $image['file_path'] . '" style="max-width: 100%; height: auto; border: 1px solid #ccc;" alt="' . htmlspecialchars($image['original_name']) . '">';
                $imageHtml .= '<div style="font-size: 12px; margin-top: 5px;">' . htmlspecialchars($image['original_name']) . '</div>';
                $imageHtml .= '</div>';
            }
            $imageHtml .= '</div>';
        } else {
            $imageHtml = '<div style="text-align: center; color: #666; font-style: italic;">No images uploaded</div>';
        }

        $html = str_replace('<!-- Photos will be inserted here -->', $imageHtml, $html);

        return $html;
    }
    
    private function processCalculatedFields($html) {
        // Calculate totals and percentages
        $maleTotal = intval($this->data['male_attendees'] ?? 0);
        $femaleTotal = intval($this->data['female_attendees'] ?? 0);
        $totalAttendees = $maleTotal + $femaleTotal;
        
        // If total attendees is provided but gender breakdown is not, use the provided total
        if (isset($this->data['total_attendees']) && $this->data['total_attendees'] > 0) {
            $totalAttendees = intval($this->data['total_attendees']);
        }
        
        // Calculate sector totals
        $ngaMale = intval($this->data['nga_male'] ?? 0);
        $ngaFemale = intval($this->data['nga_female'] ?? 0);
        $lguMale = intval($this->data['lgu_male'] ?? 0);
        $lguFemale = intval($this->data['lgu_female'] ?? 0);
        $sucMale = intval($this->data['suc_male'] ?? 0);
        $sucFemale = intval($this->data['suc_female'] ?? 0);
        $othersMale = intval($this->data['others_male'] ?? 0);
        $othersFemale = intval($this->data['others_female'] ?? 0);
        
        $certMale = intval($this->data['certificates_male'] ?? 0);
        $certFemale = intval($this->data['certificates_female'] ?? 0);
        
        // Replace calculated values
        $replacements = [
            '{{TOTAL_ATTENDEES}}' => $totalAttendees,
            '{{MALE_TOTAL}}' => $maleTotal,
            '{{FEMALE_TOTAL}}' => $femaleTotal,
            '{{NGA_MALE}}' => $ngaMale,
            '{{NGA_FEMALE}}' => $ngaFemale,
            '{{LGU_MALE}}' => $lguMale,
            '{{LGU_FEMALE}}' => $lguFemale,
            '{{SUC_MALE}}' => $sucMale,
            '{{SUC_FEMALE}}' => $sucFemale,
            '{{OTHERS_MALE}}' => $othersMale,
            '{{OTHERS_FEMALE}}' => $othersFemale,
            '{{CERT_MALE}}' => $certMale,
            '{{CERT_FEMALE}}' => $certFemale
        ];
        
        foreach ($replacements as $placeholder => $value) {
            $html = str_replace($placeholder, $value, $html);
        }
        
        return $html;
    }
    
    private function getReportTemplate() {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>After Training Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { height: 80px; margin-bottom: 10px; }
        .dept-name { font-size: 14px; font-weight: bold; margin-bottom: 5px; }
        .report-title { font-size: 18px; font-weight: bold; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; vertical-align: top; }
        th { background-color: #f0f0f0; font-weight: bold; }
        .section-title { font-weight: bold; background-color: #f0f0f0; }
        .signature-section { margin-top: 40px; }
        .signature-table { width: 100%; }
        .signature-table td { border: none; padding: 20px; text-align: center; }
        .photo-section { min-height: 200px; border: 1px solid #000; padding: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <img src="../assets/images/dict-logo.png" alt="DICT Logo" class="logo">
        <div class="dept-name">REPUBLIC OF THE PHILIPPINES</div>
        <div class="dept-name">DEPARTMENT OF INFORMATION AND</div>
        <div class="dept-name">COMMUNICATIONS TECHNOLOGY</div>
        <div class="report-title">AFTER TRAINING REPORT</div>
    </div>
    
    <table>
        <tr>
            <th colspan="4" class="section-title">I. TRAINING DETAILS</th>
        </tr>
        <tr>
            <td><strong>Course Title:</strong></td>
            <td colspan="3">{{COURSE_TITLE}}</td>
        </tr>
        <tr>
            <td><strong>Course Code:</strong></td>
            <td colspan="3">{{COURSE_CODE}}</td>
        </tr>
        <tr>
            <td><strong>Date:</strong></td>
            <td>{{DATE}}</td>
            <td><strong>Time:</strong></td>
            <td>{{TIME}}</td>
        </tr>
        <tr>
            <td><strong>Duration:</strong></td>
            <td colspan="3">{{DURATION}}</td>
        </tr>
        <tr>
            <td><strong>Venue:</strong></td>
            <td colspan="3">{{VENUE}}</td>
        </tr>
        <tr>
            <td><strong>Resource Person:</strong></td>
            <td colspan="3">{{RESOURCE_PERSON}}</td>
        </tr>
        <tr>
            <td><strong>Platform Used:</strong></td>
            <td>{{PLATFORM_USED}}</td>
            <td><strong>Mode:</strong></td>
            <td>{{MODE}}</td>
        </tr>
        <tr>
            <td><strong>Target Participants:</strong></td>
            <td colspan="3">{{TARGET_PARTICIPANTS}}</td>
        </tr>
        <tr>
            <td><strong>Total # of Attendees:</strong><br><em>(with breakdown submitted Evaluation Form/Output)</em></td>
            <td>{{TOTAL_ATTENDEES}}</td>
            <td><strong>Male</strong><br>{{MALE_TOTAL}}</td>
            <td><strong>Female</strong><br>{{FEMALE_TOTAL}}</td>
        </tr>
        <tr>
            <td colspan="4" style="text-align: center;"><strong>Number of Beneficiary/ies with Sex Disaggregation</strong></td>
        </tr>
        <tr>
            <td><strong>Sector Category:</strong></td>
            <td></td>
            <td><strong>Male</strong></td>
            <td><strong>Female</strong></td>
        </tr>
        <tr>
            <td></td>
            <td><strong>NGA:</strong></td>
            <td>{{NGA_MALE}}</td>
            <td>{{NGA_FEMALE}}</td>
        </tr>
        <tr>
            <td></td>
            <td><strong>LGU:</strong></td>
            <td>{{LGU_MALE}}</td>
            <td>{{LGU_FEMALE}}</td>
        </tr>
        <tr>
            <td></td>
            <td><strong>SUC:</strong></td>
            <td>{{SUC_MALE}}</td>
            <td>{{SUC_FEMALE}}</td>
        </tr>
        <tr>
            <td></td>
            <td><strong>Others:</strong></td>
            <td>{{OTHERS_MALE}}</td>
            <td>{{OTHERS_FEMALE}}</td>
        </tr>
        <tr>
            <td><strong>Total # of Issued Certificates:</strong><br><em>(with breakdown submitted Evaluation Form/Output)</em></td>
            <td></td>
            <td>{{CERT_MALE}}</td>
            <td>{{CERT_FEMALE}}</td>
        </tr>
    </table>
    
    <table>
        <tr>
            <th class="section-title">II. RATIONALE</th>
        </tr>
        <tr>
            <td style="min-height: 100px;">{{RATIONALE}}</td>
        </tr>
    </table>
    
    <table>
        <tr>
            <th class="section-title">III. OBJECTIVES</th>
        </tr>
        <tr>
            <td style="min-height: 100px;">{{OBJECTIVES}}</td>
        </tr>
    </table>
    
    <table>
        <tr>
            <th class="section-title">IV. Topics Covered:</th>
        </tr>
        <tr>
            <td style="min-height: 150px;">{{TOPICS_COVERED}}</td>
        </tr>
    </table>
    
    <table>
        <tr>
            <th class="section-title">V. ISSUES AND CONCERNS</th>
            <th class="section-title">VI. RECOMMENDATION</th>
        </tr>
        <tr>
            <td style="min-height: 150px; width: 50%;">{{ISSUES_CONCERNS}}</td>
            <td style="min-height: 150px; width: 50%;">{{RECOMMENDATIONS}}</td>
        </tr>
    </table>
    
    <table>
        <tr>
            <th class="section-title">VII. PLANS AND ACTION ITEMS (NEXT STEPS)</th>
        </tr>
        <tr>
            <td style="min-height: 100px;">{{PLANS_ACTION_ITEMS}}</td>
        </tr>
    </table>
    
    <table>
        <tr>
            <th class="section-title">VIII. PHOTO DOCUMENTATION</th>
        </tr>
        <tr>
            <td class="photo-section">
                <!-- Photos will be inserted here -->
            </td>
        </tr>
    </table>
    
    <div class="signature-section">
        <table class="signature-table">
            <tr>
                <td style="width: 50%;">
                    <div>Prepared by:</div>
                    <br><br><br>
                    <div><strong>{{PREPARED_BY}}</strong></div>
                    <div>{{PREPARED_BY_POSITION}}</div>
                </td>
                <td style="width: 50%;">
                    <div>Noted by:</div>
                    <br><br><br>
                    <div><strong>{{NOTED_BY}}</strong></div>
                    <div>{{NOTED_BY_POSITION}}</div>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>';
    }
    
    public function generatePDF($outputPath = null) {
        if (!$outputPath) {
            $outputPath = 'generated_reports/report_' . $this->sessionId . '_' . date('Y-m-d_H-i-s') . '.pdf';
        }
        
        $html = $this->generateHTML();
        
        // For now, we\'ll save as HTML. In production, you\'d use a library like TCPDF or wkhtmltopdf
        $htmlPath = str_replace('.pdf', '.html', $outputPath);
        file_put_contents($htmlPath, $html);
        
        return $htmlPath;
    }
}
?>
