/* Custom styles for Post-Activity Report Generation System */

:root {
    --primary-color: #0066cc;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

/* Header Styles */
.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #004499 100%) !important;
}

/* Navigation Styles */
.list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.list-group-item i {
    width: 20px;
    margin-right: 10px;
}

/* Card Styles */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--primary-color);
    border-radius: 10px 10px 0 0 !important;
    padding: 1.25rem;
}

.card-header h4, .card-header h5 {
    margin-bottom: 0;
    color: var(--dark-color);
}

.card-header p {
    margin-bottom: 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #004499 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #004499 0%, #003366 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 102, 204, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

/* Progress Bar Styles */
.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 15px;
    transition: width 0.6s ease;
}

/* File Upload Styles */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(0, 102, 204, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.1);
}

/* File List Styles */
.file-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background-color: white;
    transition: all 0.3s ease;
}

.file-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.file-icon {
    font-size: 2rem;
    margin-right: 1rem;
}

.file-info {
    flex-grow: 1;
}

.file-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.file-details {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.file-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* Data Review Styles */
.data-field {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: white;
}

.data-field-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.confidence-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
}

.confidence-high {
    background-color: #d4edda;
    color: #155724;
}

.confidence-medium {
    background-color: #fff3cd;
    color: #856404;
}

.confidence-low {
    background-color: #f8d7da;
    color: #721c24;
}

.source-file {
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin-top: 0.25rem;
}

/* Report Preview Styles */
.report-preview {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    margin-top: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.report-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

.report-logo {
    height: 80px;
    margin-bottom: 1rem;
}

.report-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.report-subtitle {
    color: var(--secondary-color);
    font-size: 1rem;
}

.report-section {
    margin-bottom: 2rem;
}

.report-section-title {
    font-weight: bold;
    color: var(--dark-color);
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.report-table th,
.report-table td {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    text-align: left;
}

.report-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .file-icon {
        margin-bottom: 0.5rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow-md {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.border-radius-lg {
    border-radius: 15px !important;
}
