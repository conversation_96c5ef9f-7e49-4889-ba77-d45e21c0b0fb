# Post-Activity Report Generation System

A comprehensive web-based system that automatically generates standardized post-activity reports from uploaded files. The system extracts relevant information from various document types and populates a professional template matching the Department of Information and Communications Technology (DICT) After Training Report format.

## Features

### Core Functionality
- **Multi-format File Upload**: Accepts PDF, DOC, DOCX, TXT, Excel, and image files
- **Automatic Data Extraction**: Intelligently extracts relevant information from uploaded documents
- **Template-based Report Generation**: Creates reports that match the exact DICT template format
- **Image Organization**: Automatically organizes and inserts images into the photo documentation section
- **Review and Edit Interface**: Allows users to review and modify extracted data before final report generation
- **Professional Formatting**: Maintains exact template formatting and layout

### File Types Supported
- **Point of Instruction Documents**: PDF, DOC, DOCX, TXT
- **Activity Design Documents**: PDF, DOC, DOCX, TXT  
- **Evaluation Forms/Results**: PDF, DOC, DOCX, TXT, Excel files
- **Supporting Images**: JPG, PNG, GIF, BMP
- **Additional Materials**: Any file type

### User Workflow
1. Upload activity-related files through the web interface
2. System automatically processes and extracts information
3. Review and edit extracted data as needed
4. Generate final post-activity report
5. Download completed report in HTML/PDF format

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- WAMP/XAMPP for local development

### Setup Instructions

1. **Clone/Download the project** to your web server directory:
   ```
   c:\wamp64\www\post\
   ```

2. **Database Configuration**:
   - The system will automatically create the database and tables on first run
   - Default configuration uses:
     - Host: localhost
     - Database: post_activity_reports
     - Username: root
     - Password: (empty)
   - Modify `config/database.php` if needed

3. **File Permissions**:
   - Ensure the following directories are writable:
     - `uploads/`
     - `generated_reports/`
     - `temp/`

4. **Access the System**:
   - Open your browser and navigate to: `http://localhost/post/`
   - The system will automatically initialize the database on first access

## Usage Guide

### 1. Upload Files
- Navigate to the "Upload Files" section
- Select files by category:
  - Point of Instruction Documents
  - Activity Design Documents
  - Evaluation Forms/Results
  - Supporting Images
  - Additional Materials
- Click "Upload Files" to process

### 2. Extract Data
- Go to the "Extract Data" section
- Click "Start Data Extraction" to automatically process uploaded files
- The system will extract relevant information based on predefined keywords

### 3. Review & Edit
- Navigate to "Review & Edit" to see extracted data
- Each field shows:
  - Extracted value
  - Confidence score
  - Source file
- Edit any field as needed - changes are automatically saved

### 4. Generate Report
- Go to "Generate Report" section
- Click "Generate Report" to create the final document
- Preview the report and download as needed

### 5. Report History
- View previously generated reports in the "Report History" section

## System Architecture

### Database Tables
- **reports**: Main report records
- **uploaded_files**: File metadata and extraction status
- **extracted_data**: Extracted field values with confidence scores
- **template_fields**: Template field definitions and extraction keywords

### Key Components
- **File Upload Handler** (`api/upload.php`): Processes file uploads
- **Data Extractor** (`includes/functions.php`): Extracts text and data from files
- **Report Generator** (`includes/report_generator.php`): Creates formatted reports
- **Template Engine**: Populates the DICT template with extracted data

### File Structure
```
post/
├── api/                    # API endpoints
├── assets/                 # CSS, JS, images
├── config/                 # Database configuration
├── generated_reports/      # Generated report files
├── includes/              # Core PHP functions
├── uploads/               # Uploaded files storage
├── utils/                 # Utility scripts
└── index.php             # Main application
```

## Template Fields

The system extracts and maps data to the following template sections:

### Training Details
- Course Title, Course Code
- Date, Time, Duration
- Venue, Resource Person
- Platform Used, Mode
- Target Participants

### Attendance Information
- Total attendees with gender breakdown
- Sector categories (NGA, LGU, SUC, Others)
- Issued certificates count

### Content Sections
- Rationale
- Objectives
- Topics Covered
- Issues and Concerns
- Recommendations
- Plans and Action Items

### Documentation
- Photo Documentation (automatic image insertion)
- Signature section (Prepared by, Noted by)

## Customization

### Adding New Template Fields
1. Add field definition to `template_fields` table
2. Update extraction keywords for automatic detection
3. Modify report template in `includes/report_generator.php`

### Modifying Extraction Logic
- Edit keyword patterns in `includes/functions.php`
- Adjust confidence scoring algorithms
- Add support for new file formats

### Template Customization
- Modify HTML template in `includes/report_generator.php`
- Update CSS styles in the template
- Add new sections or modify existing layout

## Troubleshooting

### Common Issues
1. **File Upload Fails**: Check file permissions and upload limits
2. **Database Connection Error**: Verify database credentials
3. **Text Extraction Issues**: Ensure proper file formats and encoding
4. **Report Generation Fails**: Check template syntax and data completeness

### Debug Mode
- Enable error reporting in PHP for detailed error messages
- Check browser console for JavaScript errors
- Review server logs for PHP errors

## Future Enhancements

### Planned Features
- PDF generation using libraries like TCPDF or wkhtmltopdf
- Advanced OCR for scanned documents
- Machine learning for improved data extraction
- Multi-language support
- Email notification system
- Advanced reporting analytics

### Integration Possibilities
- Google Sheets integration for data export
- Document management system integration
- Workflow automation
- API endpoints for external systems

## Support

For technical support or feature requests, please refer to the system documentation or contact the development team.

## License

This system is developed for the Department of Information and Communications Technology and is intended for internal use.
