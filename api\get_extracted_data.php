<?php
session_start();
require_once '../includes/functions.php';

header('Content-Type: application/json');

try {
    $data = getExtractedDataForReview();
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
